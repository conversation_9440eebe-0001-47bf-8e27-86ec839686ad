using Application.Core;
using FluentValidation.Results;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;

namespace Application.Extensions;

[ExcludeFromCodeCoverage]
public static class ValidationFailureExtensions
{
    public static IEnumerable<ErrorMessage> ToErrorMessages(
        this IEnumerable<ValidationFailure> failures)
    {
        return failures.Select(x => new ErrorMessage(
            x.ErrorCode,
            x.ErrorMessage));
    }
}
