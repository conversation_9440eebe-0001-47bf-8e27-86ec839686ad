using Application.Slide2.CreateS01File;
using Application.Slide2.GetS01File;
using Microsoft.Extensions.DependencyInjection;

namespace Application;

public static class UseCaseCfg
{
    public static IServiceCollection AddUseCases(
        this IServiceCollection services)
    {
        services.AddSingleton<ICreateS01FileUseCase, CreateS01FileUseCase>();
        services.AddSingleton<IGetS01FileUseCase, GetS01FileUseCase>();
        services.AddSingleton<S01BackgroundTaskQueue>();
        services.AddHostedService<S01QueuedHostedService>();

        return services;
    }
}
