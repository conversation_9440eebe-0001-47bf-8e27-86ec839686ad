using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Model.Slide2.CreateS01File.Request;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Observability.Logs;
using Application.Observability.Metrics;
using Application.Observability.Logs;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Timeout;
using Slide2Logging = Application.Observability.Logs.Slide2Logging;

namespace Application.Slide2.CreateS01File;

public class S01QueuedHostedService : BackgroundService
{
    private readonly S01BackgroundTaskQueue _taskQueue;
    private readonly Slide2Options _slide2Options;
    private readonly IWebHostEnvironment _webHostEnvironment;
    private readonly ILogger<S01QueuedHostedService> _logger;
    private readonly Slide2MetricHandler _metricHandler;
    private readonly ResiliencePipeline _resiliencePipeline;

    public S01QueuedHostedService(
        S01BackgroundTaskQueue taskQueue,
        IWebHostEnvironment app,
        IOptions<Slide2Options> slide2Options,
        ILogger<S01QueuedHostedService> logger,
        Slide2MetricHandler metricHandler)
    {
        _taskQueue = taskQueue;
        _webHostEnvironment = app;
        _slide2Options = slide2Options.Value;
        _logger = logger;
        _metricHandler = metricHandler;

        _resiliencePipeline = new ResiliencePipelineBuilder()
            .AddTimeout(
                new TimeoutStrategyOptions
                {
                    Timeout = TimeSpan.FromMinutes(
                        _slide2Options.ExecutionTimeoutInMinutes)
                })
            .Build();
    }

    protected override async Task ExecuteAsync(
        CancellationToken stoppingToken)
    {
        try
        {
            var tasks = Enumerable.Range(1, 20)
                .Select(async _ =>
                {
                    while (!stoppingToken.IsCancellationRequested)
                    {
                        var payload = await _taskQueue.DequeueAsync(
                            stoppingToken);

                        await DoWork(payload, stoppingToken);
                    }
                });

            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            _logger.LogUnexpectedException(ex);
        }
    }

    private async Task DoWork(
        (string fileName, CreateS01FileRequest request) input,
        CancellationToken cancellationToken)
    {
        var request = input.request;
        var fileName = input.fileName;

        if (request == null || string.IsNullOrEmpty(fileName))
        {
            _logger.LogInvalidRequest();
            return;
        }

        await File.WriteAllBytesAsync(
            @$"{_webHostEnvironment.WebRootPath}\{fileName}.sli",
            Convert.FromBase64String(request.SliBase64),
            cancellationToken);

        await File.WriteAllBytesAsync(
            @$"{_webHostEnvironment.WebRootPath}\{fileName}.sltm",
            Convert.FromBase64String(request.SltmBase64),
            cancellationToken);

        _logger.LogInitialFilesCreated(fileName);

        var process = new Process
        {
            StartInfo = new ProcessStartInfo(_slide2Options.ASlidewPath)
            {
                WindowStyle = ProcessWindowStyle.Minimized,
                Arguments =
                    @$"{_webHostEnvironment.WebRootPath}\{fileName}.sli",
                ErrorDialog = false,
                CreateNoWindow = true,
                ErrorDialogParentHandle = IntPtr.Zero,
            },
        };

        var stopWatch = new Stopwatch();

        try
        {
            stopWatch.Start();

            await _resiliencePipeline.ExecuteAsync(
                async token =>
                {
                    process.Start();
                    _logger.LogProcessStarted(fileName, process.Id);

                    await process.WaitForExitAsync(token);
                    _logger.LogProcessFinished(fileName, process.Id);
                },
                cancellationToken);

            _metricHandler.IncrementSlide2AnalysisCompleted();
        }
        catch (TimeoutRejectedException ex)
        {
            _logger.LogProcessTimedOut(fileName, process.Id, ex);
            _metricHandler.IncrementSlide2AnalysisTimedOut();
        }
        catch (Exception ex)
        {
            _logger.LogProcessFailed(fileName, process.Id, ex);
            _metricHandler.IncrementSlide2AnalysisFailed();
        }
        finally
        {
            if (!process.HasExited)
            {
                process.Kill();
            }

            process.Dispose();
            stopWatch.Stop();

            _metricHandler.IncrementSlide2AnalysisDuration(stopWatch.Elapsed);
        }

        File.Delete(@$"{_webHostEnvironment.WebRootPath}\{fileName}.sli");
        File.Delete(@$"{_webHostEnvironment.WebRootPath}\{fileName}.sltm");
        File.Delete(
            @$"{_webHostEnvironment.WebRootPath}\{fileName}.timing");

        _logger.LogInitialFilesDeleted(fileName);
    }
}