using Application.Core;
using Application.Extensions;
using Model.Slide2.CreateS01File.Request;
using System;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<string>;

namespace Application.Slide2.CreateS01File;

public sealed class CreateS01FileUseCase : ICreateS01FileUseCase
{
    private readonly CreateS01FileRequestValidator _requestValidator = new();
    private readonly S01BackgroundTaskQueue _taskQueue;

    public CreateS01FileUseCase(
        S01BackgroundTaskQueue taskQueue)
    {
        _taskQueue = taskQueue;
    }


    public async Task<UseCaseResponse<string>> Execute(CreateS01FileRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(string.Empty);
            }

            var validationResult = await _requestValidator
                .ValidateAsync(request);

            if (!validationResult.IsValid)
            {
                return BadRequest(string.Empty,
                    validationResult.Errors.ToErrorMessages());
            }

            var fileName = $"{request.Identifier}_{Guid.NewGuid():N}";

            _taskQueue.QueueBackgroundWorkItem(fileName, request);

            return Ok(fileName);
        }
        catch (Exception e)
        {
            return InternalServerError(string.Empty, errors: e.ToErrorMessages("000"));
        }
    }
}