using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using Model.Slide2.CreateS01File.Request;

namespace Application.Slide2.CreateS01File;

public sealed class S01BackgroundTaskQueue
{
    private readonly ConcurrentQueue<(string, CreateS01FileRequest)> _workItems = new();
    private readonly SemaphoreSlim _signal = new(0);

    public void QueueBackgroundWorkItem(string job, CreateS01FileRequest request)
    {
        _workItems.Enqueue((job, request));
        _signal.Release();
    }

    public async Task<(string, CreateS01FileRequest)> DequeueAsync(CancellationToken cancellationToken)
    {
        await _signal.WaitAsync(cancellationToken);
        _workItems.TryDequeue(out var job);
        return job;
    }
}