using System;
using System.Diagnostics.Metrics;
using static Application.Observability.ApplicationConstants;
using static Application.Observability.Metrics.Slide2MetricsConstants;
using static Application.Observability.Metrics.MeasurementUnits;

namespace Application.Observability.Metrics;

public class Slide2MetricHandler
{
    private readonly Counter<int> _slide2AnalysisCompleted;
    private readonly Counter<int> _slide2AnalysisTimedOut;
    private readonly Counter<int> _slide2AnalysisFailed;
    private readonly Counter<double> _slide2AnalysisDuration;

    public Slide2MetricHandler(IMeterFactory meterFactory)
    {
        var meter = meterFactory.Create(ServiceName, ServiceVersion);

        _slide2AnalysisCompleted = meter.CreateCounter<int>(
            AnalysisCompleted,
            Occurrences);

        _slide2AnalysisTimedOut = meter.CreateCounter<int>(
            AnalysisTimedOut,
            Occurrences);

        _slide2AnalysisFailed = meter.CreateCounter<int>(
            AnalysisFailed,
            Occurrences);

        _slide2AnalysisDuration = meter.CreateCounter<double>(
            AnalysisDuration,
            Seconds);
    }
    
    public void IncrementSlide2AnalysisCompleted(int quantity = 1)
    {
        _slide2AnalysisCompleted.Add(quantity);
    }

    public void IncrementSlide2AnalysisTimedOut(int quantity = 1)
    {
        _slide2AnalysisTimedOut.Add(quantity);
    }

    public void IncrementSlide2AnalysisFailed(int quantity = 1)
    {
        _slide2AnalysisFailed.Add(quantity);
    }

    public void IncrementSlide2AnalysisDuration(TimeSpan time)
    {
        _slide2AnalysisDuration.Add(time.TotalSeconds);
    }
}
