namespace Application.Observability.Metrics;

internal static class Slide2MetricsConstants
{
    /// <summary>
    /// Number of stability analysis sent to Slide2 executable that successfully completed.
    /// </summary>
    public const string AnalysisCompleted =
        "slide2_api.stability_analysis.completed";

    /// <summary>
    /// Number of stability analysis sent to Slide2 executable that timed out.
    /// </summary>
    public const string AnalysisTimedOut =
        "slide2_api.stability_analysis.timed_out";

    /// <summary>
    /// Number of stability analysis sent to Slide2 executable that failed because of unknown reasons.
    /// </summary>
    public const string AnalysisFailed = "slide2_api.stability_analysis.failed";

    /// <summary>
    /// Duration of stability analysis processed by Slide2 executable.
    /// </summary>
    public const string AnalysisDuration =
        "slide2_api.stability_analysis.duration";
}