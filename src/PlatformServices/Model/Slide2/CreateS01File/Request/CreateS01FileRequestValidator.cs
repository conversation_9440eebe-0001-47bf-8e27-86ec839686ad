using FluentValidation;

namespace Model.Slide2.CreateS01File.Request;

public class CreateS01FileRequestValidator
    : AbstractValidator<CreateS01FileRequest>
{
    public CreateS01FileRequestValidator()
    {
        CascadeMode = CascadeMode.Continue;

        RuleFor(request => request.Identifier).NotEmpty();
        RuleFor(request => request.SliBase64).NotEmpty();
        RuleFor(request => request.SltmBase64).NotEmpty();
    }
}
