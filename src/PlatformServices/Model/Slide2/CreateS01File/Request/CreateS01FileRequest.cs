using System.Text.Json.Serialization;

namespace Model.Slide2.CreateS01File.Request;

public sealed record CreateS01FileRequest
{
    [JsonPropertyName("identifier")]
    public string Identifier { get; init; }
        
    private string _sliBase64;

    [JsonPropertyName("sli_base64")]
    public string SliBase64 { get => _sliBase64; set => _sliBase64 = RemoveHtmlSourceIdentifier(value); }

    private string _sltmBase64;

    [JsonPropertyName("sltm_base64")]
    public string SltmBase64 { get => _sltmBase64; set => _sltmBase64 = RemoveHtmlSourceIdentifier(value); }

    private static string RemoveHtmlSourceIdentifier(string base64)
    {
        if (string.IsNullOrEmpty(base64))
        {
            return base64;
        }

        var splitedBase64 = base64.Split(",");

        return splitedBase64.Length == 1 ? splitedBase64[0] : splitedBase64[1];
    }
}