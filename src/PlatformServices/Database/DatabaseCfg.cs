using Dapper;
using FluentMigrator.Runner;
using Microsoft.AspNetCore.Builder;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace Database
{
    public static class DatabaseCfg
    {
        public static IApplicationBuilder Migrate(this IApplicationBuilder app, string connectionString)
        {
            CreateDatabaseIfNotExists(connectionString);

            using var scope = app.ApplicationServices.CreateScope();
            var runner = scope.ServiceProvider.GetService<IMigrationRunner>() ?? throw new InvalidOperationException("Runner not found");
            runner.ListMigrations();

            runner.MigrateUp();
            return app;
        }

        public static IServiceProvider AddFluentMigrator(
           this IServiceCollection services,
           string connectionString)
        {
            return services
                .AddFluentMigratorCore()
                .ConfigureRunner(rb => rb
                    .AddSqlServer()
                    //.WithGlobalCommandTimeout(TimeSpan.FromMinutes(5))
                    .WithGlobalConnectionString(connectionString)
                    .ScanIn(typeof(DatabaseCfg).Assembly).For.Migrations())
                .AddLogging(lb => lb.AddFluentMigratorConsole())
                .BuildServiceProvider(false);
        }

        private static void CreateDatabaseIfNotExists(string connectionString)
        {
            var builder = new SqlConnectionStringBuilder(connectionString);
            var name = builder.InitialCatalog;
            builder.InitialCatalog = "master";

            var parameters = new DynamicParameters();
            parameters.Add("name", name);

            using var connection = ApplicationDatabase.GetConnection(builder.ConnectionString);
            connection.Execute(@$"
                IF NOT EXISTS(SELECT * FROM sys.databases WHERE name = '{name}')
                BEGIN
                    CREATE DATABASE [{name}]
                END");
        }
    }
}
