using Api.Configuration;
using Api.Configuration.JsonConverter;
using Api.Configuration.Swagger;
using Api.Extensions;
using Application;
using Asp.Versioning;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.IO;
using System.IO.Compression;
using System.Net;
using Azure.Monitor.OpenTelemetry.AspNetCore;
using Microsoft.Extensions.Logging;
using OpenTelemetry.Logs;

var builder = WebApplication.CreateBuilder(new WebApplicationOptions
{
    ContentRootPath = Path.GetFullPath(Directory.GetCurrentDirectory()),
    WebRootPath = "wwwroot",
    Args = args,
});

builder.WebHost
    .UseStaticWebAssets()
    .UseKestrel(options =>
    {
        options.Limits.MinRequestBodyDataRate = null;
        options.Limits.MaxRequestBodySize = null;
    });

ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;

var azureMonitorOptions = builder.Configuration.GetSection("AzureMonitor").Get<AzureMonitorOptions>();

builder.Configuration
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true)
    .AddEnvironmentVariables();

builder.Services
    .Configure<GzipCompressionProviderOptions>(options =>
    {
        options.Level = CompressionLevel.Fastest;
    })
    .Configure<Slide2Options>(builder.Configuration.GetSection("Slide2"))
    .AddTelemetry(azureMonitorOptions)
    .AddUseCases()
    .AddEndpointsApiExplorer()
    .AddResponseCompression()
    .AddCors(options => options.AddDefaultPolicy(builder =>
    {
        builder.AllowAnyHeader();
        builder.AllowAnyMethod();
        builder.AllowAnyOrigin();
    }))
    .AddSwagger(builder.Configuration)
    .AddLocalization()
    .AddControllers()
    .AddJsonOptions();

builder.Services
    .AddResponseCompression(options =>
    {
        options.Providers.Add<GzipCompressionProvider>();
        options.Providers.Add<BrotliCompressionProvider>();
    })
    .AddApiVersioning(options =>
    {
        options.AssumeDefaultVersionWhenUnspecified = true;
        options.DefaultApiVersion = new ApiVersion(1, 0);
        options.ReportApiVersions = true;
    });

builder.Services
    .AddHealthChecks();

var app = builder.Build();

app.UseStaticFiles();

LocalizationCfg.ConfigureCulture("en-US");

if (app.Environment.IsDevelopment() || app.Environment.IsLocal())
{
    app
        .UseDeveloperExceptionPage()
        .UseSwagger()
        .UseSwaggerUI(options =>
        {
            options.OAuthClientId(builder.Configuration["Auth:Clients:ClientId"]);
            options.OAuth2RedirectUrl(builder.Configuration["Swagger:BaseUrl"] + builder.Configuration["Swagger:OAuth2RedirectEndpoint"]);
            options.OAuthAdditionalQueryStringParams(new() { { "nonce", "b20qlv2lg525sc1" } });
            options.OAuthAppName("Slide2 API - Swagger");
            options.RoutePrefix = "swagger";
            options.SwaggerEndpoint("v1/swagger.json", "Api v1");
        });
}

app.UseResponseCompression()
   .UseRouting()
   .UseCors()
   .UseAuthentication();

app.UseAuthorization();
app.MapControllers();
app.UseEndpoints(endpoints =>
{
    endpoints.MapHealthChecks("/health");
    endpoints.MapControllers();
});

await app.RunAsync();