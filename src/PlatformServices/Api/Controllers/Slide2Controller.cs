using System.Collections.Generic;
using System.Threading.Tasks;
using Api.Extensions;
using Application.Core;
using Application.Slide2.CreateS01File;
using Application.Slide2.GetS01File;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Model.Slide2.CreateS01File.Request;
using Model.Slide2.GetS01File.Response;

namespace Api.Controllers;

[ApiController]
[ApiVersion("1")]
[Route("api/v{version:apiVersion}/slide2")]
public class Slide2Controller : ControllerBase
{
    [HttpPost("s01")]
    [AllowAnonymous]
    [ProducesResponseType(200, Type = typeof(string))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> CreateS01File(
        [FromServices] ICreateS01FileUseCase useCase,
        [FromBody] CreateS01FileRequest request)
    {
        var result = await useCase.Execute(request);

        return result.ToObjectResult();
    }

    [HttpGet("s01/{id}")]
    [AllowAnonymous]
    [ProducesResponseType(200, Type = typeof(GetS01FileResponse))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> GetS01File(
        [FromServices] IGetS01FileUseCase useCase,
        [FromRoute] string id)
    {
        var result = await useCase.Execute(id);

        return result.ToObjectResult();
    }
}