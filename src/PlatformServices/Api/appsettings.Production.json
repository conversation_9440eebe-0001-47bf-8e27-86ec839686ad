{"Apis": {"Keycloak": {"Url": "https://logisoil.eastus2.cloudapp.azure.com/auth", "Realm": "logisoil"}}, "AzureMonitor": {"ConnectionString": "InstrumentationKey=829b3957-e1dc-4cb3-a9b1-68212847f7e4;IngestionEndpoint=https://westus2-2.in.applicationinsights.azure.com/;LiveEndpoint=https://westus2.livediagnostics.monitor.azure.com/;ApplicationId=126f7122-73a5-4354-afbb-1b00a020593c", "SamplingRatio": 1, "EnableLiveMetrics": false, "DisableOfflineStorage": true}, "Slide2": {"SlidePath": "C:\\Program Files\\Rocscience\\Slide2\\slide.exe", "ASlidewPath": "C:\\Program Files\\Rocscience\\Slide2\\aslidew.exe", "ExecutionTimeoutInMinutes": 15}, "Auth": {"Clients": {"ClientSecret": "********************************", "ClientId": "logisoil-clients-api"}, "BaseUrl": "https://logisoil.eastus2.cloudapp.azure.com/auth/realms/logisoil", "AuthorizationEndpoint": "/protocol/openid-connect/auth", "MetadataEndpoint": "/.well-known/openid-configuration", "TokenEndpoint": "/protocol/openid-connect/token", "Audiences": ["logisoil-workflows-api", "logisoil-clients-api"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Swagger": {"BaseUrl": "https://localhost:51232/swagger", "OAuth2RedirectEndpoint": "/oauth2-redirect.html"}}