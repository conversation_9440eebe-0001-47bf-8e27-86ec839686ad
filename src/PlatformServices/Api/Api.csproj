<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<UserSecretsId>794cd32d-49bf-4774-97d4-eb0d5c348259</UserSecretsId>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="wwwroot\dxf\**" />
	  <Compile Remove="wwwroot\output\**" />
	  <Content Remove="wwwroot\dxf\**" />
	  <Content Remove="wwwroot\output\**" />
	  <EmbeddedResource Remove="wwwroot\dxf\**" />
	  <EmbeddedResource Remove="wwwroot\output\**" />
	  <None Remove="wwwroot\dxf\**" />
	  <None Remove="wwwroot\output\**" />
	</ItemGroup>

	<ItemGroup>
		<None Include="..\..\.editorconfig" Link=".editorconfig" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Asp.Versioning.Mvc" Version="6.0.0" />
		<PackageReference Include="Azure.Monitor.OpenTelemetry.AspNetCore" Version="1.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.4" />
		<PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.9.0" />
		<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.9.0" />
		<PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.9.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.9.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="5.6.3" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Application\Application.csproj" />
		<ProjectReference Include="..\Model.Core\Model.Core.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="wwwroot\" />
	</ItemGroup>

	<ProjectExtensions>
		<VisualStudio>
			<UserProperties appsettings_1json__JsonSchema="" />
		</VisualStudio>
	</ProjectExtensions>

</Project>