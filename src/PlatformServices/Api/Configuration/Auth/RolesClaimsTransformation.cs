using Microsoft.AspNetCore.Authentication;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;

namespace Api.Configuration.Auth
{
    /// <summary>
    /// Transforms keycloak roles in the resource_access claim to jwt role claims.
    /// </summary>
    public class RolesClaimsTransformation : IClaimsTransformation
    {
        private readonly string _roleClaimType = "role";

        /// <summary>
        /// Provides a central transformation point to change the specified principal.
        /// </summary>
        /// <param name="principal">The <see cref="T:System.Security.Claims.ClaimsPrincipal" /> to transform.</param>
        /// <returns>
        /// The transformed principal.
        /// </returns>
        public Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
        {
            var result = principal.Clone();

            if (result.Identity is not ClaimsIdentity identity)
            {
                return Task.FromResult(result);
            }

            var resourceAccessValue = principal.FindFirst("realm_access")?.Value;

            if (string.IsNullOrWhiteSpace(resourceAccessValue))
            {
                return Task.FromResult(result);
            }

            using var resourceAccess = JsonDocument.Parse(resourceAccessValue);

            var clientRoles = resourceAccess
                .RootElement
                .GetProperty("roles");

            foreach (var role in clientRoles.EnumerateArray())
            {
                var value = role.GetString();
                if (!string.IsNullOrWhiteSpace(value))
                {
                    identity.AddClaim(new Claim(_roleClaimType, value));
                }
            }

            return Task.FromResult(result);
        }
    }
}
