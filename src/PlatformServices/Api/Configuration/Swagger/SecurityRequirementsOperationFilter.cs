using Microsoft.AspNetCore.Authorization;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Collections.Generic;
using System.Linq;

namespace Api.Configuration.Swagger
{
    public class SecurityRequirementsOperationFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            if (context.MethodInfo.DeclaringType == null)
            {
                return;
            }

            var anonControllerScope =
                context
                    .MethodInfo
                    .DeclaringType
                    .GetCustomAttributes(true)
                    .OfType<AllowAnonymousAttribute>();

            var anonMethodScope =
                context
                    .MethodInfo
                    .GetCustomAttributes(true)
                    .OfType<AllowAnonymousAttribute>();

            // only add authorization specification information if there is at least one 'Authorize' in the chain and NO method-level 'AllowAnonymous'
            if (anonMethodScope.Any() || anonControllerScope.Any())
            {
                return;
            }

            // add generic message if the controller methods dont already specify the response type
            if (!operation.Responses.ContainsKey("401"))
            {
                operation.Responses.Add("401", new OpenApiResponse { Description = "If Authorization header not present, has no value or no valid jwt bearer token" });
            }

            if (!operation.Responses.ContainsKey("403"))
            {
                operation.Responses.Add("403", new OpenApiResponse { Description = "If user not authorized to perform requested action" });
            }

            var jwtAuthScheme = new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                },

            };

            operation.Security = new List<OpenApiSecurityRequirement>
            {
                new()
                {
                    [ jwtAuthScheme ] = new List<string>()
                }
            };
        }
    }
}
