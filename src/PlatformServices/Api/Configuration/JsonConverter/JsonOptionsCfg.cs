using Microsoft.Extensions.DependencyInjection;

namespace Api.Configuration.JsonConverter
{
    public static class JsonOptionsCfg
    {
        public static void AddJsonOptions(this IMvcBuilder mvcBuilder)
        {
            mvcBuilder.AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new TrimStringConverter());
            });
        }
    }
}
