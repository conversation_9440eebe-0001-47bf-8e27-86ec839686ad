{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:36041", "sslPort": 44353}}, "$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"Api": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:51232"}}}